﻿Imports System.IO
Imports OpenTK
Imports OpenTK.Graphics.OpenGL

Public Class Form1

    Private points As New Dictionary(Of String, Tuple(Of Double, Double, Double))
    Private edges As New List(Of Tuple(Of String, String))

    Private Sub Form1_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        GLControl1.MakeCurrent()
        GL.ClearColor(Color.MidnightBlue)
        LblOrigin.Text = "Origin: (0, 0, 0)"
        LblAxis.Text = "Axis: (0, 0, 1)"
        LblAngle.Text = "Angle: 0"
        LblPosition.Text = "Position: (0, 0, 0)"
    End Sub

    Private Sub GLControl1_Paint(sender As Object, e As PaintEventArgs) Handles GLControl1.Paint
        GL.Clear(ClearBufferMask.ColorBufferBit Or ClearBufferMask.DepthBufferBit)
        GL.MatrixMode(MatrixMode.Projection)
        GL.LoadIdentity()
        GL.Ortho(-100, 100, -100, 100, -1, 1)
        GL.MatrixMode(MatrixMode.Modelview)
        GL.LoadIdentity()

        ' Draw edges as white lines
        GL.Color3(Color.White)
        GL.Begin(PrimitiveType.Lines)
        For Each edge In edges
            If points.ContainsKey(edge.Item1) AndAlso points.ContainsKey(edge.Item2) Then
                Dim p1 = points(edge.Item1)
                Dim p2 = points(edge.Item2)
                GL.Vertex2(p1.Item1, p1.Item2)
                GL.Vertex2(p2.Item1, p2.Item2)
            End If
        Next
        GL.End()

        GLControl1.SwapBuffers()
    End Sub

    Private Sub BtnLoad_Click(sender As Object, e As EventArgs) Handles BtnLoad.Click
        Dim ofd As New OpenFileDialog()
        ofd.Filter = "STEP Files (*.step;*.stp)|*.step;*.stp|All Files (*.*)|*.*"
        ofd.Title = "Open STEP File"
        If ofd.ShowDialog() = DialogResult.OK Then
            LblFileName.Text = "File: " & Path.GetFileName(ofd.FileName)
            Dim lines = File.ReadAllLines(ofd.FileName)
            Dim matched = lines.Where(Function(line) _
                line.Contains("AXIS2_PLACEMENT_3D") OrElse
                line.Contains("DIRECTION") OrElse
                line.Contains("CARTESIAN_POINT") OrElse
                line.Contains("ANGLE")
            ).Take(4).ToArray()
            TxtMatchedLines.Text = String.Join(Environment.NewLine, matched)
            LblStatus.Text = "Status: Loaded"

            ' Parse points
            points.Clear()
            For Each line In lines
                If line.Contains("CARTESIAN_POINT") Then
                    Dim id = line.Split("="c)(0).Trim()
                    Dim coords = ParseCartesianPoint(line)
                    If coords IsNot Nothing Then points(id) = coords
                End If
            Next

            ' Parse edges
            edges.Clear()
            For Each line In lines
                If line.Contains("EDGE_CURVE") Then
                    Dim ids = ParseEdgeCurve(line)
                    If ids IsNot Nothing Then edges.Add(ids)
                End If
            Next

            GLControl1.Invalidate()
        Else
            LblStatus.Text = "Status: Load canceled"
        End If
    End Sub

    ' Parse a STEP CARTESIAN_POINT line
    Private Function ParseCartesianPoint(line As String) As Tuple(Of Double, Double, Double)
        ' Example: #12=CARTESIAN_POINT('',(1.0,2.0,3.0));
        Dim startIdx = line.IndexOf("("c)
        Dim endIdx = line.IndexOf(")", startIdx + 1)
        If startIdx = -1 OrElse endIdx = -1 Then Return Nothing
        Dim inner = line.Substring(startIdx + 1, endIdx - startIdx - 1)
        If inner.Contains("(") Then inner = inner.Substring(inner.IndexOf("("c) + 1)
        Dim parts = inner.Split(","c)
        If parts.Length < 3 Then Return Nothing
        Dim x, y, z As Double
        If Double.TryParse(parts(0), x) AndAlso Double.TryParse(parts(1), y) AndAlso Double.TryParse(parts(2), z) Then
            Return Tuple.Create(x, y, z)
        End If
        Return Nothing
    End Function

    ' Parse a STEP EDGE_CURVE line
    Private Function ParseEdgeCurve(line As String) As Tuple(Of String, String)
        ' Example: #20=EDGE_CURVE('',#12,#13,#30,.T.);
        Dim parts = line.Split(","c)
        If parts.Length < 4 Then Return Nothing
        Dim startId = parts(1).Trim().Replace("'", "").Replace("(", "").Replace(")", "")
        Dim endId = parts(2).Trim().Replace("'", "").Replace("(", "").Replace(")", "")
        Return Tuple.Create(startId, endId)
    End Function

End Class