{"version": 3, "targets": {"net8.0-windows7.0": {"OpenTK/4.9.4": {"type": "package", "dependencies": {"OpenTK.Audio.OpenAL": "4.9.4", "OpenTK.Compute": "4.9.4", "OpenTK.Core": "4.9.4", "OpenTK.Graphics": "4.9.4", "OpenTK.Input": "4.9.4", "OpenTK.Mathematics": "4.9.4", "OpenTK.Windowing.Common": "4.9.4", "OpenTK.Windowing.Desktop": "4.9.4", "OpenTK.Windowing.GraphicsLibraryFramework": "4.9.4"}}, "OpenTK.Audio.OpenAL/4.9.4": {"type": "package", "dependencies": {"OpenTK.Core": "[4.9.4, 4.10.0)", "OpenTK.Mathematics": "[4.9.4, 4.10.0)"}, "compile": {"lib/netcoreapp3.1/OpenTK.Audio.OpenAL.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/OpenTK.Audio.OpenAL.dll": {"related": ".pdb;.xml"}}}, "OpenTK.Compute/4.9.4": {"type": "package", "compile": {"lib/netcoreapp3.1/OpenTK.Compute.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/OpenTK.Compute.dll": {"related": ".pdb;.xml"}}}, "OpenTK.Core/4.9.4": {"type": "package", "compile": {"lib/netstandard2.1/OpenTK.Core.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.1/OpenTK.Core.dll": {"related": ".pdb;.xml"}}}, "OpenTK.GLControl/4.0.2": {"type": "package", "dependencies": {"OpenTK.Graphics": "4.9.3", "OpenTK.Windowing.Desktop": "4.9.3"}, "compile": {"lib/netcoreapp3.1/OpenTK.GLControl.dll": {"related": ".pdb"}}, "runtime": {"lib/netcoreapp3.1/OpenTK.GLControl.dll": {"related": ".pdb"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WindowsForms"]}, "OpenTK.Graphics/4.9.4": {"type": "package", "dependencies": {"OpenTK.Core": "[4.9.4, 4.10.0)", "OpenTK.Mathematics": "[4.9.4, 4.10.0)"}, "compile": {"lib/netcoreapp3.1/OpenTK.Graphics.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/OpenTK.Graphics.dll": {"related": ".pdb;.xml"}}}, "OpenTK.Input/4.9.4": {"type": "package", "compile": {"lib/netstandard2.0/OpenTK.Input.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/OpenTK.Input.dll": {"related": ".pdb;.xml"}}}, "OpenTK.Mathematics/4.9.4": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/netcoreapp3.1/OpenTK.Mathematics.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/OpenTK.Mathematics.dll": {"related": ".pdb;.xml"}}}, "OpenTK.redist.glfw/********": {"type": "package", "compile": {"lib/netstandard2.0/_._": {}}, "runtime": {"lib/netstandard2.0/_._": {}}, "runtimeTargets": {"runtimes/linux-x64/native/libglfw.so.3": {"assetType": "native", "rid": "linux-x64"}, "runtimes/osx-arm64/native/libglfw.3.dylib": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/libglfw.3.dylib": {"assetType": "native", "rid": "osx-x64"}, "runtimes/win-x64/native/glfw3.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/glfw3.dll": {"assetType": "native", "rid": "win-x86"}}}, "OpenTK.Windowing.Common/4.9.4": {"type": "package", "dependencies": {"OpenTK.Core": "[4.9.4, 4.10.0)", "OpenTK.Mathematics": "[4.9.4, 4.10.0)"}, "compile": {"lib/netcoreapp3.1/OpenTK.Windowing.Common.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/OpenTK.Windowing.Common.dll": {"related": ".pdb;.xml"}}}, "OpenTK.Windowing.Desktop/4.9.4": {"type": "package", "dependencies": {"OpenTK.Core": "[4.9.4, 4.10.0)", "OpenTK.Mathematics": "[4.9.4, 4.10.0)", "OpenTK.Windowing.Common": "[4.9.4, 4.10.0)", "OpenTK.Windowing.GraphicsLibraryFramework": "[4.9.4, 4.10.0)"}, "compile": {"lib/netcoreapp3.1/OpenTK.Windowing.Desktop.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/OpenTK.Windowing.Desktop.dll": {"related": ".pdb;.xml"}}}, "OpenTK.Windowing.GraphicsLibraryFramework/4.9.4": {"type": "package", "dependencies": {"OpenTK.Core": "[4.9.4, 4.10.0)", "OpenTK.redist.glfw": "********"}, "compile": {"lib/netcoreapp3.1/OpenTK.Windowing.GraphicsLibraryFramework.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netcoreapp3.1/OpenTK.Windowing.GraphicsLibraryFramework.dll": {"related": ".pdb;.xml"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}}}, "libraries": {"OpenTK/4.9.4": {"sha512": "fp9ZI6akO4ZuIGzuUE0QRU5zRJhXbSXa5racehFoTdvodhRDecXCOcH68mRsZaMwnxLO9ECnf2LGM9BmiJKvhw==", "type": "package", "path": "opentk/4.9.4", "files": [".nupkg.metadata", ".signature.p7s", "opentk.4.9.4.nupkg.sha512", "opentk.nuspec"]}, "OpenTK.Audio.OpenAL/4.9.4": {"sha512": "N/SCeFrLJ3ckUbshyfsonbtjmDxh55urGiR+Fe1iPkTWyEW5OKfQRoIOZyjEva/4eRqOXdt3bV2ka4IJ2+JvZw==", "type": "package", "path": "opentk.audio.openal/4.9.4", "files": [".nupkg.metadata", ".signature.p7s", "docs/README.md", "images/opentk-blue-hexagon.png", "lib/netcoreapp3.1/OpenTK.Audio.OpenAL.dll", "lib/netcoreapp3.1/OpenTK.Audio.OpenAL.pdb", "lib/netcoreapp3.1/OpenTK.Audio.OpenAL.xml", "opentk.audio.openal.4.9.4.nupkg.sha512", "opentk.audio.openal.nuspec"]}, "OpenTK.Compute/4.9.4": {"sha512": "30G+/9mtrUJwX5xcRyz2qcujQ2s+wDm+C7qg6jDsLyGWUdND3j/vD+nlQ6tuKuAow51zWduFVujPkaS8VrBinQ==", "type": "package", "path": "opentk.compute/4.9.4", "files": [".nupkg.metadata", ".signature.p7s", "docs/README.md", "images/opentk-blue-hexagon.png", "lib/netcoreapp3.1/OpenTK.Compute.dll", "lib/netcoreapp3.1/OpenTK.Compute.pdb", "lib/netcoreapp3.1/OpenTK.Compute.xml", "opentk.compute.4.9.4.nupkg.sha512", "opentk.compute.nuspec"]}, "OpenTK.Core/4.9.4": {"sha512": "/4Mn3ABf1xNfHMzDFUapVY2K4PG3oP/oWTQJlK0JacT20HwsNe/HPIAhnAJWz27EM3ukOJDtqyQRukvQKqkcLw==", "type": "package", "path": "opentk.core/4.9.4", "files": [".nupkg.metadata", ".signature.p7s", "docs/README.md", "images/opentk-blue-hexagon.png", "lib/netstandard2.1/OpenTK.Core.dll", "lib/netstandard2.1/OpenTK.Core.pdb", "lib/netstandard2.1/OpenTK.Core.xml", "opentk.core.4.9.4.nupkg.sha512", "opentk.core.nuspec"]}, "OpenTK.GLControl/4.0.2": {"sha512": "N4F8kFT7l+nZIpMrtnp/MdJWKv9o+OsQwuDBZxr2Qg7Hz3FeW7fgsRo0kwrpQss8/bv8AYO+xqrhpbKlSImtMw==", "type": "package", "path": "opentk.glcontrol/4.0.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/netcoreapp3.1/OpenTK.GLControl.dll", "lib/netcoreapp3.1/OpenTK.GLControl.pdb", "opentk.glcontrol.4.0.2.nupkg.sha512", "opentk.glcontrol.nuspec"]}, "OpenTK.Graphics/4.9.4": {"sha512": "yZeq5swIsRnNzifVA9fLtl+qMAQvmYiF1b7U/XQLARmmU4PYQ23lPNahqAdD22XzA21ew7eG3yDyPLkYw+hDBA==", "type": "package", "path": "opentk.graphics/4.9.4", "files": [".nupkg.metadata", ".signature.p7s", "docs/README.md", "images/opentk-blue-hexagon.png", "lib/netcoreapp3.1/OpenTK.Graphics.dll", "lib/netcoreapp3.1/OpenTK.Graphics.pdb", "lib/netcoreapp3.1/OpenTK.Graphics.xml", "lib/netstandard2.1/OpenTK.Graphics.dll", "lib/netstandard2.1/OpenTK.Graphics.pdb", "lib/netstandard2.1/OpenTK.Graphics.xml", "opentk.graphics.4.9.4.nupkg.sha512", "opentk.graphics.nuspec"]}, "OpenTK.Input/4.9.4": {"sha512": "njbC7fySQEK30rjOQ1uRjlycEkLw+UVoPT3nRQ8uBPNLotDgr47NEcPEjmJyX1ll+pm/wmtcvJIYHBRi1HbOqg==", "type": "package", "path": "opentk.input/4.9.4", "files": [".nupkg.metadata", ".signature.p7s", "docs/README.md", "images/opentk-blue-hexagon.png", "lib/netstandard2.0/OpenTK.Input.dll", "lib/netstandard2.0/OpenTK.Input.pdb", "lib/netstandard2.0/OpenTK.Input.xml", "opentk.input.4.9.4.nupkg.sha512", "opentk.input.nuspec"]}, "OpenTK.Mathematics/4.9.4": {"sha512": "2ucF25RVJzdSLXUHgjAgB058gVfSgerrR5pLCn9J+Bnyqi45NrBfPu9wyTT35ZCjYwLJCu/HJMnzKFLjq+uFIA==", "type": "package", "path": "opentk.mathematics/4.9.4", "files": [".nupkg.metadata", ".signature.p7s", "docs/README.md", "images/opentk-blue-hexagon.png", "lib/netcoreapp3.1/OpenTK.Mathematics.dll", "lib/netcoreapp3.1/OpenTK.Mathematics.pdb", "lib/netcoreapp3.1/OpenTK.Mathematics.xml", "lib/netstandard2.1/OpenTK.Mathematics.dll", "lib/netstandard2.1/OpenTK.Mathematics.pdb", "lib/netstandard2.1/OpenTK.Mathematics.xml", "opentk.mathematics.4.9.4.nupkg.sha512", "opentk.mathematics.nuspec"]}, "OpenTK.redist.glfw/********": {"sha512": "HEjbdk0wWxSRrXHl3DSmrA8trecndgJpAHHloJJ1vPseNfeu/ynmrH/LturU1KguRn4r3IzZ81UIp5xRjnyahg==", "type": "package", "path": "opentk.redist.glfw/********", "files": [".nupkg.metadata", ".signature.p7s", "COPYING.md", "lib/netstandard2.0/_._", "opentk.png", "opentk.redist.glfw.********.nupkg.sha512", "opentk.redist.glfw.nuspec", "runtimes/linux-x64/native/libglfw.so.3", "runtimes/osx-arm64/native/libglfw.3.dylib", "runtimes/osx-x64/native/libglfw.3.dylib", "runtimes/win-x64/native/glfw3.dll", "runtimes/win-x86/native/glfw3.dll"]}, "OpenTK.Windowing.Common/4.9.4": {"sha512": "xNjvKjoptAh6w5/YYIx/9sNv3/A2VVlTahlekTlLP+qfNyig+0jBuLHO4bWELWZ7Ta3Y1m/2Pvk0Z1KCG8VZOA==", "type": "package", "path": "opentk.windowing.common/4.9.4", "files": [".nupkg.metadata", ".signature.p7s", "docs/README.md", "images/opentk-blue-hexagon.png", "lib/netcoreapp3.1/OpenTK.Windowing.Common.dll", "lib/netcoreapp3.1/OpenTK.Windowing.Common.pdb", "lib/netcoreapp3.1/OpenTK.Windowing.Common.xml", "lib/netstandard2.1/OpenTK.Windowing.Common.dll", "lib/netstandard2.1/OpenTK.Windowing.Common.pdb", "lib/netstandard2.1/OpenTK.Windowing.Common.xml", "opentk.windowing.common.4.9.4.nupkg.sha512", "opentk.windowing.common.nuspec"]}, "OpenTK.Windowing.Desktop/4.9.4": {"sha512": "LBX4jwCneISIBST2vWYLI8cdR8tLwHNcswK1decElbTAXMB+H6uFIoF41ld+af4q3TsHpjR5fyP2ilbTK3T0rA==", "type": "package", "path": "opentk.windowing.desktop/4.9.4", "files": [".nupkg.metadata", ".signature.p7s", "docs/README.md", "images/opentk-blue-hexagon.png", "lib/netcoreapp3.1/OpenTK.Windowing.Desktop.dll", "lib/netcoreapp3.1/OpenTK.Windowing.Desktop.pdb", "lib/netcoreapp3.1/OpenTK.Windowing.Desktop.xml", "opentk.windowing.desktop.4.9.4.nupkg.sha512", "opentk.windowing.desktop.nuspec"]}, "OpenTK.Windowing.GraphicsLibraryFramework/4.9.4": {"sha512": "wNlxnPrD7veMV8EGTTi/sCDeg5V8w/18vC0aw0Ao69eNneD7YSq9Ad+C389rVA957eHNR6Xieu/5qzHQeEOyxQ==", "type": "package", "path": "opentk.windowing.graphicslibraryframework/4.9.4", "files": [".nupkg.metadata", ".signature.p7s", "docs/README.md", "images/opentk-blue-hexagon.png", "lib/netcoreapp3.1/OpenTK.Windowing.GraphicsLibraryFramework.dll", "lib/netcoreapp3.1/OpenTK.Windowing.GraphicsLibraryFramework.pdb", "lib/netcoreapp3.1/OpenTK.Windowing.GraphicsLibraryFramework.xml", "opentk.windowing.graphicslibraryframework.4.9.4.nupkg.sha512", "opentk.windowing.graphicslibraryframework.nuspec"]}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"sha512": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "type": "package", "path": "system.runtime.compilerservices.unsafe/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Runtime.CompilerServices.Unsafe.dll", "lib/net461/System.Runtime.CompilerServices.Unsafe.xml", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt"]}}, "projectFileDependencyGroups": {"net8.0-windows7.0": ["OpenTK >= 4.9.4", "OpenTK.GLControl >= 4.0.2"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Python\\VIEWER\\3d viewer\\3d viewer.vbproj", "projectName": "3d viewer", "projectPath": "E:\\Python\\VIEWER\\3d viewer\\3d viewer.vbproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Python\\VIEWER\\3d viewer\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"OpenTK": {"target": "Package", "version": "[4.9.4, )"}, "OpenTK.GLControl": {"target": "Package", "version": "[4.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}}